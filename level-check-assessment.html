<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Level Check Assessment - Alternative to selfmax.ai</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header .subtitle {
            color: #34495e;
            font-size: 1.2em;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .header .description {
            color: #7f8c8d;
            font-size: 1em;
            margin-bottom: 20px;
        }

        .framework-info {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }

        .framework-info h3 {
            color: #2980b9;
            margin-bottom: 15px;
        }

        .levels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .level-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-size: 0.9em;
        }

        .level-item strong {
            color: #2c3e50;
        }

        .instructions {
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }

        .instructions h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .instructions ul {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #856404;
        }

        .start-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .progress-container {
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #495057;
        }

        .question-container {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-left: 5px solid #667eea;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .question-number {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
        }

        .question-category {
            color: #6c757d;
            font-weight: 600;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .question-text {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            line-height: 1.4;
        }

        .options-container {
            display: grid;
            gap: 12px;
        }

        .option {
            background: white;
            padding: 18px 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .option-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9em;
            flex-shrink: 0;
        }

        .option.selected .option-number {
            background: white;
            color: #667eea;
        }

        .option-text {
            flex: 1;
            font-size: 1em;
            line-height: 1.5;
        }

        .submit-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 18px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.3em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            display: block;
            margin: 40px auto;
        }

        .submit-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.6);
        }

        .hidden {
            display: none !important;
        }
        .results {
            background: #f8f9fa;
            padding: 40px;
            border-radius: 15px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .results h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }

        .primary-result {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .primary-result h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .primary-result p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .level-distribution {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .level-distribution h4 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .distribution-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .distribution-item:last-child {
            border-bottom: none;
        }

        .distribution-bar {
            width: 60%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .distribution-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .interpretation {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .interpretation h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .interpretation p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .next-steps {
            background: #d1ecf1;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #17a2b8;
            margin: 20px 0;
        }

        .next-steps h4 {
            color: #0c5460;
            margin-bottom: 15px;
        }

        .disclaimer {
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
            margin: 30px 0;
            font-size: 0.95em;
        }

        .disclaimer h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .disclaimer p {
            color: #856404;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .question-text {
                font-size: 1.1em;
            }

            .option {
                padding: 15px;
            }

            .levels-grid {
                grid-template-columns: 1fr;
            }

            .question-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Free Level Check Assessment</h1>
            <div class="subtitle">Alternative to selfmax.ai Level Check</div>
            <div class="description">Based on hoe_math's 9 Levels Framework</div>
            <div class="description">Discover your highest level of thinking when concentrating</div>
        </div>

        <div id="intro-section">
            <div class="framework-info">
                <h3>The 9 Levels Framework</h3>
                <p>This assessment is based on hoe_math's developmental framework, which maps 9 distinct levels of human consciousness and thinking:</p>
                <div class="levels-grid">
                    <div class="level-item">
                        <strong>Level 1: Desire (Survive)</strong> - Basic needs and survival focused
                    </div>
                    <div class="level-item">
                        <strong>Level 2: Connect (Appease)</strong> - Social connection and group harmony
                    </div>
                    <div class="level-item">
                        <strong>Level 3: Take (Power)</strong> - Personal power and dominance
                    </div>
                    <div class="level-item">
                        <strong>Level 4: Belong (Conform)</strong> - Rules, order, and conformity
                    </div>
                    <div class="level-item">
                        <strong>Level 5: Achieve (Individual)</strong> - Success and achievement oriented
                    </div>
                    <div class="level-item">
                        <strong>Level 6: Include (Pluralistic)</strong> - Inclusivity and multiple perspectives
                    </div>
                    <div class="level-item">
                        <strong>Level 7: Integrate (Systematic)</strong> - Systems thinking and integration
                    </div>
                    <div class="level-item">
                        <strong>Level 8: Construct-Aware</strong> - Witnessing mind construction
                    </div>
                    <div class="level-item">
                        <strong>Level 9: Meta-Systemic</strong> - Transcendent perspective
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h3>Assessment Instructions</h3>
                <ul>
                    <li><strong>Choose responses representing your highest thinking</strong> when concentrating</li>
                    <li><strong>Be honest rather than trying to score high</strong> - accuracy is more valuable</li>
                    <li><strong>Trust your first strong resonance</strong> to each question</li>
                    <li><strong>This measures your peak capacity</strong>, not average daily operation</li>
                    <li><strong>Take your time</strong> - there are 25 comprehensive questions</li>
                    <li><strong>Higher levels aren't "better"</strong> - each serves important functions</li>
                </ul>
            </div>

            <div style="text-align: center;">
                <button class="start-button" onclick="startAssessment()">Start Assessment</button>
            </div>
        </div>

        <div id="assessment-section" class="hidden">
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Question 0 of 25</div>
            </div>

            <div id="questionsContainer"></div>

            <div style="text-align: center;">
                <button class="submit-button hidden" id="submitButton" onclick="calculateResults()">
                    Get My Results
                </button>
            </div>
        </div>

        <div id="results-section" class="hidden">
            <!-- Results will be populated by JavaScript -->
        </div>
    </div>

    <script>
        // Assessment data - 25 comprehensive questions
        const assessmentData = {
            levels: {
                1: {
                    name: "Desire (Survive)",
                    description: "Basic needs and wants - survival focused",
                    characteristics: ["Immediate needs", "Survival instinct", "Fight/flight responses", "Basic drives"],
                    interpretation: "You operate primarily from survival-focused thinking, prioritizing immediate needs and safety. This level is essential for basic functioning and provides the foundation for all higher development."
                },
                2: {
                    name: "Connect (Appease)",
                    description: "Understanding others have needs - social connection",
                    characteristics: ["Tribal bonds", "Group loyalty", "Social harmony", "Belonging"],
                    interpretation: "You prioritize social connections and group harmony. Your thinking centers around maintaining relationships and ensuring everyone's basic needs are met within your community."
                },
                3: {
                    name: "Take (Power/Egocentric)",
                    description: "Manipulating social connections for personal gain",
                    characteristics: ["Personal power", "Dominance", "Winning", "Control"],
                    interpretation: "You focus on personal power and achieving dominance in situations. Your thinking is strategic about gaining advantage and asserting control over your environment and relationships."
                },
                4: {
                    name: "Belong (Conform)",
                    description: "Following rules to fit in - conformity",
                    characteristics: ["Rules and order", "Duty", "Authority", "Conformity"],
                    interpretation: "You operate from a framework of rules, duty, and proper order. Your thinking emphasizes following established procedures and maintaining social structures and hierarchies."
                },
                5: {
                    name: "Achieve (Individualistic)",
                    description: "Creating your own success path - achievement oriented",
                    characteristics: ["Success", "Achievement", "Strategy", "Individual excellence"],
                    interpretation: "You think strategically about achieving personal goals and success. Your focus is on individual excellence, rational planning, and creating your own path to achievement."
                },
                6: {
                    name: "Include (Pluralistic)",
                    description: "Validating all perspectives - inclusivity",
                    characteristics: ["Diversity", "Inclusion", "Multiple perspectives", "Egalitarianism"],
                    interpretation: "You prioritize inclusivity and honoring multiple perspectives. Your thinking emphasizes understanding different viewpoints and ensuring all voices are heard and valued."
                },
                7: {
                    name: "Integrate (Systematic)",
                    description: "Understanding how different levels work together",
                    characteristics: ["Systems thinking", "Integration", "Complexity", "Developmental awareness"],
                    interpretation: "You think in terms of complex systems and how different perspectives and levels work together. Your approach integrates multiple viewpoints while understanding their developmental context."
                },
                8: {
                    name: "Construct-Aware",
                    description: "Witnessing mind construction in real-time",
                    characteristics: ["Metacognition", "Mental construction awareness", "Witnessing", "Deconstruction"],
                    interpretation: "You observe how your mind constructs reality and meaning. Your thinking includes awareness of mental patterns, assumptions, and the constructed nature of perspectives."
                },
                9: {
                    name: "Meta-Systemic",
                    description: "Highest documented level - transcendent perspective",
                    characteristics: ["Transcendence", "Unity consciousness", "Meta-perspective", "Non-dual awareness"],
                    interpretation: "You operate from a transcendent perspective that includes and transcends all other levels. Your thinking flows from pure awareness beyond constructed mental frameworks."
                }
            },
            questions: [
                {
                    id: 1,
                    category: "Problem Solving",
                    question: "When facing a major life challenge, what's your typical approach?",
                    options: {
                        1: "Focus on immediate survival and getting through it",
                        2: "Consider how it affects my relationships and seek support",
                        3: "Look for ways to gain advantage or control the situation",
                        4: "Follow established procedures and social expectations",
                        5: "Create a strategic plan to achieve my desired outcome",
                        6: "Consider multiple perspectives and seek inclusive solutions",
                        7: "Analyze the interconnected systems and root causes",
                        8: "Observe my mental patterns and assumptions about the challenge",
                        9: "Transcend the problem-solution duality and see the bigger picture"
                    }
                },
                {
                    id: 2,
                    category: "Relationships",
                    question: "How do you typically view conflicts with others?",
                    options: {
                        1: "Threats to my safety and well-being that must be avoided",
                        2: "Disruptions to harmony that need to be smoothed over",
                        3: "Opportunities to assert dominance or gain upper hand",
                        4: "Violations of proper social rules that need correction",
                        5: "Challenges to overcome strategically for better outcomes",
                        6: "Chances to understand different viewpoints and find common ground",
                        7: "Complex interactions between different developmental levels",
                        8: "Projections of internal mental constructs onto external situations",
                        9: "Temporary patterns in the flow of consciousness"
                    }
                },
                {
                    id: 3,
                    category: "Decision Making",
                    question: "When making important decisions, what matters most to you?",
                    options: {
                        1: "What will keep me safe and meet my immediate needs",
                        2: "What will maintain my connections and not hurt others",
                        3: "What will give me the most power and control",
                        4: "What is the right thing according to established principles",
                        5: "What will most effectively achieve my goals",
                        6: "What honors everyone's perspectives and values",
                        7: "What serves the whole system while respecting all parts",
                        8: "What emerges from awareness beyond my conditioned patterns",
                        9: "What flows naturally from pure consciousness itself"
                    }
                },
                {
                    id: 4,
                    category: "Self-Concept",
                    question: "How do you primarily see yourself?",
                    options: {
                        1: "A being trying to survive and get basic needs met",
                        2: "Someone who belongs to and cares for my tribe/group",
                        3: "An individual who must assert myself to get what I want",
                        4: "A responsible person who follows proper rules and duties",
                        5: "An achiever who creates success through my own efforts",
                        6: "A compassionate person who values all beings equally",
                        7: "An integral thinker who sees complex interconnections",
                        8: "Awareness observing the construction of a 'self'",
                        9: "Pure consciousness temporarily appearing as a person"
                    }
                },
                {
                    id: 5,
                    category: "Worldview",
                    question: "How do you understand reality?",
                    options: {
                        1: "A dangerous place where I must fight for survival",
                        2: "A network of relationships and tribal connections",
                        3: "A power game where the strong dominate the weak",
                        4: "An ordered system with clear right and wrong ways",
                        5: "A field of opportunities to achieve and succeed",
                        6: "A rich tapestry of diverse perspectives and experiences",
                        7: "A complex web of interconnected systems and levels",
                        8: "A construction of mind that can be witnessed and transcended",
                        9: "The play of consciousness appearing as multiplicity"
                    }
                },
                {
                    id: 6,
                    category: "Leadership Style",
                    question: "When in a leadership position, how do you tend to operate?",
                    options: {
                        1: "Command and control to ensure group survival",
                        2: "Build consensus and maintain group harmony",
                        3: "Assert authority and demand compliance through force",
                        4: "Establish clear rules and ensure everyone follows them",
                        5: "Set ambitious goals and motivate high performance",
                        6: "Create inclusive environments where all voices are heard",
                        7: "Design systems that work for all stakeholders",
                        8: "Hold space for emergence while staying unattached to outcomes",
                        9: "Serve as an empty vessel for collective wisdom to flow through"
                    }
                },
                {
                    id: 7,
                    category: "Learning Approach",
                    question: "How do you prefer to learn and grow?",
                    options: {
                        1: "Trial and error focused on immediate practical needs",
                        2: "Learning from elders and traditional group wisdom",
                        3: "Mastering skills that give me competitive advantage",
                        4: "Following established curricula and proven methods",
                        5: "Strategic learning that advances my goals efficiently",
                        6: "Exploring diverse perspectives and experiential learning",
                        7: "Studying complex systems and their interactions",
                        8: "Investigating the nature of mind and consciousness itself",
                        9: "Learning happens spontaneously through pure awareness"
                    }
                },
                {
                    id: 8,
                    category: "Meaning & Purpose",
                    question: "What gives your life the deepest meaning?",
                    options: {
                        1: "Staying alive and meeting my basic needs",
                        2: "Being connected to and caring for my people",
                        3: "Having power and influence over my environment",
                        4: "Living according to higher moral principles",
                        5: "Achieving my personal goals and vision",
                        6: "Contributing to justice and wellbeing for all",
                        7: "Participating in the evolution of consciousness",
                        8: "Recognizing the constructed nature of all meaning",
                        9: "Being an expression of pure consciousness/love"
                    }
                },
                {
                    id: 9,
                    category: "Response to Criticism",
                    question: "When someone criticizes you, what's your typical reaction?",
                    options: {
                        1: "Feel threatened and defensive about my safety/position",
                        2: "Worry about damage to relationships and seek to repair",
                        3: "Get angry and look for ways to retaliate or dominate",
                        4: "Check if I violated proper rules and correct accordingly",
                        5: "Analyze if it's valid feedback for achieving my goals",
                        6: "Try to understand their perspective and find mutual ground",
                        7: "See it as information about different developmental stages",
                        8: "Observe my emotional reactions and mental stories arising",
                        9: "Rest in awareness, seeing criticism as just phenomena arising"
                    }
                },
                {
                    id: 10,
                    category: "Change & Uncertainty",
                    question: "How do you respond to major changes or uncertainty?",
                    options: {
                        1: "Feel anxious and focus on securing basic safety",
                        2: "Seek comfort and support from my trusted group",
                        3: "Try to control outcomes and dominate the situation",
                        4: "Look for clear rules and established ways to handle it",
                        5: "See opportunities and strategically adapt my plans",
                        6: "Embrace the diversity of possibilities and perspectives",
                        7: "Understand it as natural evolution of complex systems",
                        8: "Observe attachment to certainty and rest in not-knowing",
                        9: "Flow with change as the natural movement of consciousness"
                    }
                },
                {
                    id: 11,
                    category: "Values & Ethics",
                    question: "What guides your moral and ethical decisions?",
                    options: {
                        1: "What keeps me alive and safe",
                        2: "What my group/tribe considers right",
                        3: "What serves my personal interests and power",
                        4: "Universal moral principles and divine commands",
                        5: "Rational analysis of consequences and effectiveness",
                        6: "Caring for all beings and social justice",
                        7: "What serves the evolution of all systems involved",
                        8: "Awareness of the constructed nature of all moral systems",
                        9: "What emerges spontaneously from pure compassion"
                    }
                },
                {
                    id: 12,
                    category: "Spirituality & Transcendence",
                    question: "How do you relate to spirituality or transcendence?",
                    options: {
                        1: "Pray for protection and basic needs",
                        2: "Follow traditional group religious practices",
                        3: "Use spiritual practices for personal power/advantage",
                        4: "Adhere to orthodox religious doctrine and commandments",
                        5: "Explore spiritual practices that enhance performance",
                        6: "Embrace diverse spiritual traditions as equally valid",
                        7: "See spirituality as part of human developmental evolution",
                        8: "Investigate the nature of consciousness and awareness itself",
                        9: "Rest as pure awareness - the source of all spiritual seeking"
                    }
                },
                {
                    id: 13,
                    category: "Communication Style",
                    question: "How do you typically communicate in important conversations?",
                    options: {
                        1: "Direct and blunt to get basic needs met",
                        2: "Harmonious and supportive to maintain group bonds",
                        3: "Persuasive and dominant to win arguments",
                        4: "Proper and formal following social protocols",
                        5: "Strategic and clear to achieve objectives",
                        6: "Inclusive and empathetic honoring all voices",
                        7: "Multi-perspective aware of different developmental levels",
                        8: "Transparent about mental constructs and assumptions",
                        9: "Speaking from silence, letting wisdom flow through"
                    }
                },
                {
                    id: 14,
                    category: "Career & Work",
                    question: "What motivates you most in your work or career?",
                    options: {
                        1: "Having job security and meeting basic needs",
                        2: "Being part of a team and helping colleagues",
                        3: "Gaining status, recognition and beating competition",
                        4: "Fulfilling duties and following proper procedures",
                        5: "Achieving ambitious goals and professional success",
                        6: "Making a positive difference for all stakeholders",
                        7: "Contributing to sustainable, integral solutions",
                        8: "Expressing creativity while staying unattached to outcomes",
                        9: "Serving as an instrument of consciousness in the world"
                    }
                },
                {
                    id: 15,
                    category: "Money & Resources",
                    question: "How do you view money and material resources?",
                    options: {
                        1: "Essential for survival and basic security",
                        2: "Means to support my family and community",
                        3: "Tools for gaining power and status over others",
                        4: "Rewards for hard work and moral living",
                        5: "Resources to achieve my goals and fund my vision",
                        6: "Should be shared equitably to reduce suffering",
                        7: "Energy in complex economic and social systems",
                        8: "Mental constructs we collectively agree have value",
                        9: "Temporary forms that consciousness takes - neither good nor bad"
                    }
                },
                {
                    id: 16,
                    category: "Personal Growth",
                    question: "How do you approach personal development and growth?",
                    options: {
                        1: "Learn skills needed for survival and safety",
                        2: "Grow in ways that serve my relationships and tribe",
                        3: "Develop capabilities that increase my power and influence",
                        4: "Follow established self-improvement programs and principles",
                        5: "Strategically develop skills that advance my goals",
                        6: "Explore diverse approaches honoring multiple wisdom traditions",
                        7: "Understand development as evolution through various levels",
                        8: "Investigate the one who seeks to grow and develop",
                        9: "Growth happens naturally as awareness recognizes itself"
                    }
                },
                {
                    id: 17,
                    category: "Authority & Power",
                    question: "How do you relate to authority figures and power structures?",
                    options: {
                        1: "Submit when necessary for protection, resist when safe",
                        2: "Respect traditional elders and group hierarchies",
                        3: "Challenge them to assert my own dominance",
                        4: "Honor legitimate authority and proper chain of command",
                        5: "Leverage them strategically for my objectives",
                        6: "Question unjust hierarchies and advocate for equality",
                        7: "See them as developmental expressions serving different functions",
                        8: "Recognize them as mental constructs we collectively maintain",
                        9: "Neither submit to nor resist - flow with what's present"
                    }
                },
                {
                    id: 18,
                    category: "Stress & Pressure",
                    question: "How do you handle high stress or pressure situations?",
                    options: {
                        1: "Fight, flight, or freeze based on survival instincts",
                        2: "Seek support and comfort from trusted relationships",
                        3: "Dominate the situation or attack the source of pressure",
                        4: "Follow established protocols and do my duty despite stress",
                        5: "Stay focused on goals and find strategic solutions",
                        6: "Consider impact on all stakeholders and seek collaborative solutions",
                        7: "See stress as information about systemic imbalances",
                        8: "Observe stress arising in awareness without identification",
                        9: "Rest in the peace that's always present regardless of circumstances"
                    }
                },
                {
                    id: 19,
                    category: "Diversity & Differences",
                    question: "How do you respond to people very different from you?",
                    options: {
                        1: "Feel suspicious or threatened by differences",
                        2: "Accept those who fit with my group's values",
                        3: "See if I can use their differences to my advantage",
                        4: "Judge them according to proper moral standards",
                        5: "Evaluate how they might help or hinder my goals",
                        6: "Celebrate diversity and learn from all perspectives",
                        7: "Understand differences as expressions of developmental levels",
                        8: "See all differences as constructs of mind - same awareness underneath",
                        9: "Rest in the unity that includes and transcends all differences"
                    }
                },
                {
                    id: 20,
                    category: "Success & Failure",
                    question: "How do you define success and handle failure?",
                    options: {
                        1: "Success is staying alive; failure threatens survival",
                        2: "Success is group harmony; failure hurts relationships",
                        3: "Success is winning and dominating; failure shows weakness",
                        4: "Success is moral righteousness; failure means I sinned",
                        5: "Success is achieving goals; failure is feedback for strategy",
                        6: "Success is collective wellbeing; failure ignores someone's needs",
                        7: "Success is systemic health; failure comes from partial perspectives",
                        8: "Success and failure are mental constructs about what is",
                        9: "Success and failure are equal movements in consciousness"
                    }
                },
                {
                    id: 21,
                    category: "Time & Planning",
                    question: "How do you approach time management and future planning?",
                    options: {
                        1: "Focus on immediate needs, short-term survival planning",
                        2: "Follow traditional cycles and group schedules",
                        3: "Plan to maximize personal advantage and control",
                        4: "Organize time according to proper duties and moral obligations",
                        5: "Strategic planning with clear timelines to achieve objectives",
                        6: "Balance time to honor everyone's needs and schedules",
                        7: "Plan considering multiple systems and their development over time",
                        8: "Hold plans lightly, staying present to what's emerging now",
                        9: "Rest in timeless presence while letting appropriate action unfold"
                    }
                },
                {
                    id: 22,
                    category: "Health & Body",
                    question: "How do you relate to your physical health and body?",
                    options: {
                        1: "Keep it functional for basic survival needs",
                        2: "Care for it as part of caring for family and community",
                        3: "Develop it for strength, attractiveness, and competitive advantage",
                        4: "Maintain it properly as a moral and social responsibility",
                        5: "Optimize it strategically for peak performance and goal achievement",
                        6: "Honor it while being mindful of health equity and accessibility",
                        7: "See it as part of larger ecological and social systems",
                        8: "Observe the relationship between awareness and physical sensations",
                        9: "Rest as the awareness in which body sensations appear"
                    }
                },
                {
                    id: 23,
                    category: "Technology & Innovation",
                    question: "How do you approach new technologies and innovations?",
                    options: {
                        1: "Adopt what helps with basic needs, avoid what threatens security",
                        2: "Follow what my trusted community adopts",
                        3: "Use technology to gain competitive advantage and power",
                        4: "Evaluate based on moral implications and proper use",
                        5: "Strategically adopt technologies that advance my objectives",
                        6: "Consider impact on all people and advocate for equitable access",
                        7: "Understand technology as part of cultural and cognitive evolution",
                        8: "See technology as extension of mind's creative capacity",
                        9: "Neither attracted to nor averse - use what serves conscious expression"
                    }
                },
                {
                    id: 24,
                    category: "Emotions & Feelings",
                    question: "How do you typically relate to your emotions?",
                    options: {
                        1: "Feel them intensely, driven by basic survival emotions",
                        2: "Share them with trusted others to maintain connection",
                        3: "Use them strategically or suppress them to maintain power",
                        4: "Control them according to what's socially appropriate",
                        5: "Manage them effectively to support goal achievement",
                        6: "Honor all emotions as valid and seek to understand others'",
                        7: "See emotions as information about system states and needs",
                        8: "Witness emotions arising and passing in awareness",
                        9: "Rest as the peace that holds all emotional weather"
                    }
                },
                {
                    id: 25,
                    category: "Global & Environmental Issues",
                    question: "How do you approach global challenges like climate change?",
                    options: {
                        1: "Focus on how they threaten my immediate safety and resources",
                        2: "Support what my community/tribe believes about these issues",
                        3: "Look for opportunities to gain advantage or avoid personal cost",
                        4: "Follow established authorities and moral guidelines on these issues",
                        5: "Analyze rationally and support effective solutions",
                        6: "Advocate for justice and protection of all affected beings",
                        7: "Address root systemic causes and design integral solutions",
                        8: "See these as constructs while responding with compassion",
                        9: "Serve the healing of the whole from pure presence"
                    }
                }
            ]
        };

        // Assessment state
        let responses = {};
        let currentQuestion = 0;

        // Start the assessment
        function startAssessment() {
            document.getElementById('intro-section').classList.add('hidden');
            document.getElementById('assessment-section').classList.remove('hidden');
            displayQuestions();
        }

        // Display all questions
        function displayQuestions() {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';

            assessmentData.questions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-container';
                questionDiv.innerHTML = `
                    <div class="question-header">
                        <div class="question-number">Question ${question.id}</div>
                        <div class="question-category">${question.category}</div>
                    </div>
                    <div class="question-text">${question.question}</div>
                    <div class="options-container" data-question="${question.id}">
                        ${Object.entries(question.options).map(([level, text]) => `
                            <div class="option" data-level="${level}" onclick="selectOption(${question.id}, ${level})">
                                <div class="option-number">${level}</div>
                                <div class="option-text">${text}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        // Handle option selection
        function selectOption(questionId, level) {
            responses[questionId] = parseInt(level);

            // Update visual selection
            const container = document.querySelector(`[data-question="${questionId}"]`);
            container.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            container.querySelector(`[data-level="${level}"]`).classList.add('selected');

            updateProgress();

            // Show submit button when all questions are answered
            if (Object.keys(responses).length === assessmentData.questions.length) {
                document.getElementById('submitButton').classList.remove('hidden');
                document.getElementById('submitButton').scrollIntoView({behavior: 'smooth'});
            }
        }

        // Update progress bar
        function updateProgress() {
            const progress = (Object.keys(responses).length / assessmentData.questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent =
                `Question ${Object.keys(responses).length} of ${assessmentData.questions.length}`;
        }

        // Calculate and display results
        function calculateResults() {
            const responseValues = Object.values(responses);

            // Calculate level distribution
            const levelCounts = {};
            for (let i = 1; i <= 9; i++) {
                levelCounts[i] = 0;
            }
            responseValues.forEach(level => {
                levelCounts[level] = (levelCounts[level] || 0) + 1;
            });

            // Find primary level (most frequent)
            let primaryLevel = 1;
            let maxCount = 0;
            for (let level = 1; level <= 9; level++) {
                if (levelCounts[level] > maxCount) {
                    maxCount = levelCounts[level];
                    primaryLevel = level;
                }
            }

            // Calculate average
            const averageLevel = (responseValues.reduce((a, b) => a + b, 0) / responseValues.length).toFixed(1);

            displayResults(primaryLevel, levelCounts, averageLevel);
        }

        // Display results
        function displayResults(primaryLevel, levelCounts, averageLevel) {
            const level = assessmentData.levels[primaryLevel];
            const resultsDiv = document.getElementById('results-section');

            // Build distribution HTML
            let distributionHtml = '';
            for (let i = 1; i <= 9; i++) {
                const count = levelCounts[i] || 0;
                const percentage = ((count / 25) * 100).toFixed(1);
                if (count > 0) {
                    distributionHtml += `
                        <div class="distribution-item">
                            <span>Level ${i}: ${assessmentData.levels[i].name}</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill" style="width: ${percentage}%"></div>
                            </div>
                            <span>${count} responses (${percentage}%)</span>
                        </div>
                    `;
                }
            }

            resultsDiv.innerHTML = `
                <div class="results">
                    <h2>Your Assessment Results</h2>

                    <div class="primary-result">
                        <h3>Your Primary Level: ${primaryLevel}</h3>
                        <p><strong>${level.name}</strong></p>
                        <p>${level.description}</p>
                    </div>

                    <div class="level-distribution">
                        <h4>Response Distribution</h4>
                        ${distributionHtml}
                    </div>

                    <div class="interpretation">
                        <h4>Interpretation</h4>
                        <p><strong>Primary Level ${primaryLevel} (${level.name}):</strong> ${level.interpretation}</p>
                        <p><strong>Average Level:</strong> ${averageLevel}</p>
                        <p><strong>Key Characteristics:</strong> ${level.characteristics.join(', ')}</p>
                    </div>

                    <div class="next-steps">
                        <h4>Next Steps for Development</h4>
                        <p>Consider exploring the characteristics of Level ${Math.min(9, parseInt(primaryLevel) + 1)}
                        to understand your next developmental edge. Remember that growth happens gradually through
                        all levels, not by skipping stages.</p>
                        <p><strong>Important:</strong> Higher levels aren't "better" - each serves important functions
                        in human development. This assessment shows your center of gravity when focusing intensely.</p>
                    </div>

                    <div class="disclaimer">
                        <h4>Important Notes</h4>
                        <p><strong>This is a free alternative to selfmax.ai's Level Check assessment.</strong>
                        Based on publicly available information about hoe_math's framework.</p>
                        <p>For the official assessment with additional features and professional interpretation,
                        visit <a href="https://selfmax.ai" target="_blank">selfmax.ai</a></p>
                        <p>Consider this one data point in understanding yourself. Most people operate from
                        multiple levels depending on context.</p>
                    </div>
                </div>
            `;

            // Hide assessment section and show results
            document.getElementById('assessment-section').classList.add('hidden');
            resultsDiv.classList.remove('hidden');
            resultsDiv.scrollIntoView({behavior: 'smooth'});
        }
    </script>
</body>
</html>
