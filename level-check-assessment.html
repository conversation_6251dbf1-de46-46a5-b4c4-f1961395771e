<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Level Check Assessment - Alternative to selfmax.ai</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header .subtitle {
            color: #34495e;
            font-size: 1.2em;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .header .description {
            color: #7f8c8d;
            font-size: 1em;
            margin-bottom: 20px;
        }

        .framework-info {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }

        .framework-info h3 {
            color: #2980b9;
            margin-bottom: 15px;
        }

        .levels-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .level-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-size: 0.9em;
        }

        .level-item strong {
            color: #2c3e50;
        }

        .instructions {
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #ffc107;
        }

        .instructions h3 {
            color: #856404;
            margin-bottom: 15px;
        }

        .instructions ul {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #856404;
        }

        .start-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .progress-container {
            margin: 30px 0;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #495057;
        }

        .question-container {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            border-left: 5px solid #667eea;
        }

        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .question-number {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
        }

        .question-category {
            color: #6c757d;
            font-weight: 600;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .question-text {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            line-height: 1.4;
        }

        .options-container {
            display: grid;
            gap: 12px;
        }

        .option {
            background: white;
            padding: 18px 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .option-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9em;
            flex-shrink: 0;
        }

        .option.selected .option-number {
            background: white;
            color: #667eea;
        }

        .option-text {
            flex: 1;
            font-size: 1em;
            line-height: 1.5;
        }

        .submit-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 18px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.3em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            display: block;
            margin: 40px auto;
        }

        .submit-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.6);
        }

        .hidden {
            display: none !important;
        }
        .results {
            background: #f8f9fa;
            padding: 40px;
            border-radius: 15px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .results h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }

        .primary-result {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .primary-result h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .primary-result p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .level-distribution {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .level-distribution h4 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .distribution-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .distribution-item:last-child {
            border-bottom: none;
        }

        .distribution-bar {
            width: 60%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .distribution-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .interpretation {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .interpretation h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .interpretation p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .next-steps {
            background: #d1ecf1;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #17a2b8;
            margin: 20px 0;
        }

        .next-steps h4 {
            color: #0c5460;
            margin-bottom: 15px;
        }

        .disclaimer {
            background: #fff3cd;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
            margin: 30px 0;
            font-size: 0.95em;
        }

        .disclaimer h4 {
            color: #856404;
            margin-bottom: 15px;
        }

        .disclaimer p {
            color: #856404;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2em;
            }

            .question-text {
                font-size: 1.1em;
            }

            .option {
                padding: 15px;
            }

            .levels-grid {
                grid-template-columns: 1fr;
            }

            .question-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Free Level Check Assessment</h1>
            <div class="subtitle">Alternative to selfmax.ai Level Check</div>
            <div class="description">Based on hoe_math's 9 Levels Framework</div>
            <div class="description">Discover your highest level of thinking when concentrating</div>
        </div>

        <div id="intro-section">
            <div class="framework-info">
                <h3>The 9 Levels Framework</h3>
                <p>This assessment is based on hoe_math's developmental framework, which maps 9 distinct levels of human consciousness and thinking:</p>
                <div class="levels-grid">
                    <div class="level-item">
                        <strong>Level 1: Desire (Survive)</strong> - Basic needs and survival focused
                    </div>
                    <div class="level-item">
                        <strong>Level 2: Connect (Appease)</strong> - Social connection and group harmony
                    </div>
                    <div class="level-item">
                        <strong>Level 3: Take (Power)</strong> - Personal power and dominance
                    </div>
                    <div class="level-item">
                        <strong>Level 4: Belong (Conform)</strong> - Rules, order, and conformity
                    </div>
                    <div class="level-item">
                        <strong>Level 5: Achieve (Individual)</strong> - Success and achievement oriented
                    </div>
                    <div class="level-item">
                        <strong>Level 6: Include (Pluralistic)</strong> - Inclusivity and multiple perspectives
                    </div>
                    <div class="level-item">
                        <strong>Level 7: Integrate (Systematic)</strong> - Systems thinking and integration
                    </div>
                    <div class="level-item">
                        <strong>Level 8: Construct-Aware</strong> - Witnessing mind construction
                    </div>
                    <div class="level-item">
                        <strong>Level 9: Meta-Systemic</strong> - Transcendent perspective
                    </div>
                </div>
            </div>

            <div class="instructions">
                <h3>Assessment Instructions</h3>
                <ul>
                    <li><strong>Choose responses representing your highest thinking</strong> when concentrating</li>
                    <li><strong>Be honest rather than trying to score high</strong> - accuracy is more valuable</li>
                    <li><strong>Trust your first strong resonance</strong> to each question</li>
                    <li><strong>This measures your peak capacity</strong>, not average daily operation</li>
                    <li><strong>Take your time</strong> - there are 25 comprehensive questions</li>
                    <li><strong>Higher levels aren't "better"</strong> - each serves important functions</li>
                </ul>
            </div>

            <div style="text-align: center;">
                <button class="start-button" onclick="startAssessment()">Start Assessment</button>
            </div>
        </div>

        <div id="assessment-section" class="hidden">
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Question 0 of 25</div>
            </div>

            <div id="questionsContainer"></div>

            <div style="text-align: center;">
                <button class="submit-button hidden" id="submitButton" onclick="calculateResults()">
                    Get My Results
                </button>
            </div>
        </div>

        <div id="results-section" class="hidden">
            <!-- Results will be populated by JavaScript -->
        </div>
    </div>

    <script>
        // Assessment data - 25 comprehensive questions
        const assessmentData = {
            levels: {
                1: {
                    name: "Survive (Needs)",
                    description: "Focusing on immediate safety and basic needs.",
                    characteristics: ["Staying safe", "Gut instincts", "Food & shelter", "Core drives"],
                    interpretation: "Your thinking is centered on survival, safety, and meeting your most immediate needs. This is the foundational level for all humans, ensuring you have the security to operate in the world."
                },
                2: {
                    name: "Connect (Harmony)",
                    description: "Finding your place in a group and keeping the peace.",
                    characteristics: ["Belonging", "Friendship & family", "Group loyalty", "Keeping peace"],
                    interpretation: "You prioritize your connections with others and maintaining harmony in your group. You're tuned in to the needs of your people and find meaning in belonging and caring for them."
                },
                3: {
                    name: "Take (Power)",
                    description: "Making your mark on the world and getting what you want.",
                    characteristics: ["Personal power", "Winning", "Taking charge", "Being independent"],
                    interpretation: "You're focused on asserting yourself, achieving personal power, and taking control of your environment. Your thinking is often strategic, aimed at gaining an advantage and expressing your own will."
                },
                4: {
                    name: "Belong (Order)",
                    description: "Following a clear set of rules and values to live by.",
                    characteristics: ["Rules & principles", "Duty", "Right & wrong", "Responsibility"],
                    interpretation: "You operate from a strong moral compass and a belief in order, rules, and responsibility. Having a clear structure and knowing what's 'right' according to a trusted authority or system is important to you."
                },
                5: {
                    name: "Achieve (Success)",
                    description: "Setting big goals and strategically working to reach them.",
                    characteristics: ["Success", "Goal-setting", "Strategy", "Personal achievement"],
                    interpretation: "You are driven by success and personal achievement. You think strategically, set ambitious goals, and work effectively to create your own successful path in life. You value competence and results."
                },
                6: {
                    name: "Include (Community)",
                    description: "Making sure everyone feels seen, heard, and valued.",
                    characteristics: ["Inclusion", "Fairness for all", "Hearing all sides", "Community"],
                    interpretation: "You prioritize inclusivity and care deeply about fairness for all. You naturally see and value many different perspectives and work to make sure that all voices, especially those often ignored, are heard."
                },
                7: {
                    name: "Integrate (Systems)",
                    description: "Seeing the big picture and how everything connects.",
                    characteristics: ["Seeing connections", "Big-picture thinking", "Finding root causes", "Long-term view"],
                    interpretation: "You think in terms of complex systems, seeing the hidden patterns and connections that others might miss. Instead of just seeing individual problems, you understand their root causes within a larger context."
                },
                8: {
                    name: "Observe (Mind-Watcher)",
                    description: "Noticing how your own mind builds your reality.",
                    characteristics: ["Thinking about thinking", "Watching your thoughts", "Questioning assumptions", "Inner awareness"],
                    interpretation: "You have the rare ability to observe your own mind at work. You can watch your thoughts, feelings, and beliefs as they arise without being totally lost in them. You realize you are not your thoughts; you are the one aware of them."
                },
                9: {
                    name: "Unify (Transcendent)",
                    description: "Feeling a deep connection to everything, beyond ideas.",
                    characteristics: ["Oneness", "Deep connection", "Beyond the self", "Inner peace"],
                    interpretation: "You operate from a profound sense of peace and connection with all of life. The feeling of being a separate self fades, and your actions naturally flow from a place of unity and wisdom, beyond any rigid mental framework."
                }
            },
            questions: [
                {
                    id: 1,
                    category: "Problem Solving",
                    question: "When you hit a major setback or a big, unexpected problem, what's your first instinct?",
                    options: {
                        1: "Focus on immediate survival and getting through it",
                        2: "Consider how it affects my relationships and seek support",
                        3: "Look for ways to gain an advantage or control the situation",
                        4: "Follow the proper steps or what's expected of me",
                        5: "Create a strategic plan to get the outcome I want",
                        6: "Hear out everyone's perspective to find a fair solution",
                        7: "Try to map out all the moving parts to find the real source of the problem",
                        8: "Step back and notice the story I'm telling myself about the problem",
                        9: "Zoom out and see the situation from a bigger perspective, where it might not be a 'problem' to be 'fixed'"
                    }
                },
                {
                    id: 2,
                    category: "Relationships",
                    question: "When you get into a disagreement with someone, how do you see it?",
                    options: {
                        1: "As a threat to my safety or stability that I need to shut down",
                        2: "As a disruption to group harmony that needs to be smoothed over",
                        3: "As a chance to prove my point and come out on top",
                        4: "As someone breaking the rules of how people should act, and it needs to be corrected",
                        5: "As a challenge to solve so we can both get a better outcome",
                        6: "As a chance to understand their point of view, even if we don't agree",
                        7: "As a mix of people reacting based on where they're at in their own life journey",
                        8: "As a mirror showing me my own habits and emotional triggers in action",
                        9: "As a temporary storm in a teacup that will pass"
                    }
                },
                {
                    id: 3,
                    category: "Decision Making",
                    question: "When making an important decision, what matters most to you?",
                    options: {
                        1: "Whatever will keep me safe and cover my basic needs",
                        2: "Whatever will maintain my friendships and not upset my people",
                        3: "Whatever will give me the most control or put me in the best position",
                        4: "Making the 'right' choice according to my principles or a trusted authority",
                        5: "The choice that will most effectively help me achieve my goals",
                        6: "The option that respects everyone's feelings and perspectives",
                        7: "The choice that's best for everyone and everything involved in the long run",
                        8: "What feels right when I step back from my usual way of thinking",
                        9: "Trusting the answer that comes to me when I'm calm and present"
                    }
                },
                {
                    id: 4,
                    category: "Self-Concept",
                    question: "How do you primarily see yourself?",
                    options: {
                        1: "A being trying to survive and get my needs met",
                        2: "Someone who belongs to and cares for my group of people",
                        3: "An individual who has to be strong to get what I want",
                        4: "A responsible person who plays by the rules and does my duty",
                        5: "An achiever who builds my own success through hard work",
                        6: "A compassionate person who believes all people are equally important",
                        7: "Someone who sees how everything and everyone is connected",
                        8: "The awareness that is able to watch my own thoughts and feelings",
                        9: "A small part of a much larger, interconnected whole"
                    }
                },
                {
                    id: 5,
                    category: "Worldview",
                    question: "How do you generally see the world?",
                    options: {
                        1: "A tough place where you have to watch your back to survive",
                        2: "A network of groups and communities to belong to",
                        3: "A competition where the strong and smart get ahead",
                        4: "An organized system with clear rules for right and wrong",
                        5: "A world full of opportunities for me to succeed and achieve",
                        6: "A diverse tapestry of different cultures and perspectives to be valued",
                        7: "A complex web where everything affects everything else",
                        8: "Something that I can observe my own thoughts and judgments about",
                        9: "A single, unified system where everything is fundamentally connected"
                    }
                },
                {
                    id: 6,
                    category: "Leadership Style",
                    question: "When you're in charge of a group project or taking the lead with friends, what's your style?",
                    options: {
                        1: "Take command to make sure we all get through it",
                        2: "Focus on making sure everyone feels included and gets along",
                        3: "Take charge and expect people to follow my lead because I have a strong vision",
                        4: "Set up clear rules and make sure everyone does their part correctly",
                        5: "Set ambitious goals and motivate everyone to perform their best",
                        6: "Make sure everyone feels comfortable sharing their ideas and has their voice heard",
                        7: "Figure out a process that works well for everyone and helps us all succeed",
                        8: "Create a relaxed space where the best ideas can just naturally emerge from the group",
                        9: "Quiet my own ego and let the collective wisdom of the group guide us"
                    }
                },
                {
                    id: 7,
                    category: "Learning Approach",
                    question: "When it comes to learning something new, what's your preferred way to do it?",
                    options: {
                        1: "Through trial and error, focusing on what I need to know right now",
                        2: "By listening to the wisdom and traditions of my family or community",
                        3: "Mastering skills that give me a competitive edge",
                        4: "Following a structured course or a trusted, proven method",
                        5: "Learning specific skills that directly help me get ahead and achieve my goals",
                        6: "Exploring lots of different perspectives and learning from hands-on experience",
                        7: "Diving deep to understand how everything fits together in the larger system",
                        8: "Getting curious about how my own mind builds thoughts, beliefs, and feelings",
                        9: "Realizing that by just being open and paying attention, I'm constantly learning"
                    }
                },
                {
                    id: 8,
                    category: "Meaning & Purpose",
                    question: "What gives your life the deepest sense of meaning?",
                    options: {
                        1: "Feeling safe and having my basic needs covered",
                        2: "Being connected to and caring for my people",
                        3: "Having influence and being able to shape my own world",
                        4: "Living a good, moral life according to my principles",
                        5: "Achieving my goals and making my vision a reality",
                        6: "Making a positive impact and contributing to a fairer world for all",
                        7: "Being a part of the growth and evolution of things bigger than me",
                        8: "Realizing that I create my own meaning based on how I see things",
                        9: "Feeling deeply connected to the universe or a higher power"
                    }
                },
                {
                    id: 9,
                    category: "Response to Criticism",
                    question: "When someone criticizes you, what's your gut reaction?",
                    options: {
                        1: "I feel attacked and get defensive",
                        2: "I worry that it will damage our relationship and try to fix it",
                        3: "I get angry and want to prove them wrong or criticize them back",
                        4: "I check if I broke a rule or did something wrong, and correct it if I did",
                        5: "I analyze it to see if their feedback can help me improve",
                        6: "I try to understand their perspective and why they feel that way",
                        7: "I see it as useful information about where they're coming from",
                        8: "I notice my emotional reaction and the stories I start telling myself",
                        9: "I can hear it without taking it personally, seeing it as just information"
                    }
                },
                {
                    id: 10,
                    category: "Change & Uncertainty",
                    question: "How do you handle big, unexpected changes?",
                    options: {
                        1: "I feel anxious and immediately focus on getting some stability back",
                        2: "I lean on my friends and family for comfort and support",
                        3: "I try to take control of the situation to make it go my way",
                        4: "I look for a clear set of rules or a trusted person to tell me what to do",
                        5: "I see it as an opportunity and quickly adapt my plans to succeed",
                        6: "I embrace the new possibilities and try to make sure everyone is okay",
                        7: "I understand it's a natural part of how complex things grow and change",
                        8: "I notice my mind wanting certainty and try to be okay with not knowing",
                        9: "I trust the flow of life and adapt to change as it comes"
                    }
                },
                {
                    id: 11,
                    category: "Values & Ethics",
                    question: "When you have to decide what the 'right' thing to do is, what's your guide?",
                    options: {
                        1: "Whatever keeps me and mine safe",
                        2: "What my community, family, or group believes is right",
                        3: "What serves my own interests and puts me in a better position",
                        4: "A clear set of universal rules or religious beliefs about right and wrong",
                        5: "A rational analysis of what will lead to the best results",
                        6: "A deep sense of care for the well-being and justice for all people",
                        7: "What would be best for the long-term health and growth of everyone involved",
                        8: "Realizing that 'right' and 'wrong' are created by people, and I can question them",
                        9: "What feels instinctively right from a place of genuine love and compassion"
                    }
                },
                {
                    id: 12,
                    category: "Spirituality & Deeper Questions",
                    question: "How do you think about spirituality or the 'big questions' in life?",
                    options: {
                        1: "I might pray for safety and for my basic needs to be met",
                        2: "I follow the religious or spiritual traditions I was raised with",
                        3: "I might use spiritual ideas to gain personal power or insight",
                        4: "I stick to the official rules and teachings of an organized religion",
                        5: "I explore practices like meditation to improve my focus and performance",
                        6: "I believe all spiritual paths are valid ways of seeing the truth",
                        7: "I see spirituality as a natural part of human psychological development",
                        8: "I'm most interested in exploring the nature of my own consciousness",
                        9: "I feel a sense of peace just by being aware, beyond any specific belief"
                    }
                },
                {
                    id: 13,
                    category: "Communication Style",
                    question: "In an important conversation, how do you tend to communicate?",
                    options: {
                        1: "Direct and to the point to get what I need",
                        2: "Supportive and agreeable to keep the peace",
                        3: "Persuasive and confident, aiming to win the argument",
                        4: "Polite and formal, following the proper social rules",
                        5: "Clear, logical, and strategic to achieve a specific goal",
                        6: "Empathetic and inclusive, making sure everyone feels heard",
                        7: "I adapt my style, aware that people are at different levels of understanding",
                        8: "I'm open about my own thought process and assumptions",
                        9: "I speak from a place of calm observation and inner knowing"
                    }
                },
                {
                    id: 14,
                    category: "Career & Work",
                    question: "What motivates you most in your work or career?",
                    options: {
                        1: "Job security and a steady paycheck to cover my needs",
                        2: "Being part of a great team and feeling like I belong",
                        3: "Gaining status, recognition, and getting ahead of the competition",
                        4: "Doing my job well and fulfilling my duties responsibly",
                        5: "Achieving ambitious goals and building a successful career",
                        6: "Doing work that makes a positive difference for people or society",
                        7: "Creating or contributing to sustainable systems that help everyone",
                        8: "Having the freedom to be creative and not being attached to a specific outcome",
                        9: "Feeling like my work is a channel for something greater than myself"
                    }
                },
                {
                    id: 15,
                    category: "Money & Resources",
                    question: "How do you view money and material things?",
                    options: {
                        1: "As essential for survival and basic security",
                        2: "As a way to support my family and community",
                        3: "As a tool for gaining power, status, and freedom",
                        4: "As a reward for hard work and living a moral life",
                        5: "As fuel to achieve my goals and build the life I want",
                        6: "As something that should be shared more fairly to help everyone",
                        7: "As a type of energy that flows through our society's systems",
                        8: "As an idea that we all agree has value, but isn't inherently real",
                        9: "As temporary resources to be used wisely, but not to get attached to"
                    }
                },
                {
                    id: 16,
                    category: "Personal Growth",
                    question: "What's your approach to self-improvement?",
                    options: {
                        1: "I learn new skills when I need them to survive or get by",
                        2: "I try to become a better friend, partner, or community member",
                        3: "I develop skills that make me more powerful or influential",
                        4: "I follow a proven self-help program or a set of principles",
                        5: "I strategically work on skills that will help me succeed in my goals",
                        6: "I explore many different paths to growth from various cultures and traditions",
                        7: "I see my growth as moving through natural stages of human development",
                        8: "I'm curious about who I am underneath the personality I usually show",
                        9: "I believe real growth happens naturally as I become more self-aware"
                    }
                },
                {
                    id: 17,
                    category: "Authority & Power",
                    question: "How do you typically deal with people in charge (bosses, teachers, officials) and rules?",
                    options: {
                        1: "I'm wary of them, but I'll obey if I have to for my own safety",
                        2: "I respect the traditional chain of command and community leaders",
                        3: "I challenge them to assert my own strength and ideas",
                        4: "I believe in respecting legitimate authority and following the rules",
                        5: "I see them as people to network with or resources to help me succeed",
                        6: "I question authority that seems unfair and advocate for more equality",
                        7: "I understand that different types of rules and authority are needed for different situations",
                        8: "I see them as systems and roles that we all collectively agree to create",
                        9: "I don't automatically resist or obey; I respond to what the situation truly calls for"
                    }
                },
                {
                    id: 18,
                    category: "Stress & Pressure",
                    question: "How do you handle intense stress or pressure?",
                    options: {
                        1: "My survival instincts kick in: I might fight, flee, or freeze",
                        2: "I reach out to my friends or family for support",
                        3: "I try to dominate the situation or push back against what's stressing me",
                        4: "I focus on my duties and follow my routine to get through it",
                        5: "I stay focused on the goal and look for a smart way to solve the problem",
                        6: "I think about how it's affecting everyone and try to find a collaborative solution",
                        7: "I see the stress as a signal that something in the larger system is out of balance",
                        8: "I can feel the stress in my body but can also watch it without getting lost in it",
                        9: "I find a place of inner calm that is always there, no matter the external pressure"
                    }
                },
                {
                    id: 19,
                    category: "Diversity & Differences",
                    question: "How do you respond to people who are very different from you?",
                    options: {
                        1: "I'm suspicious of them and tend to stick with my own kind",
                        2: "I'm comfortable with people who share my group's values",
                        3: "I might see their differences as a weakness or something I can use to my advantage",
                        4: "I might judge them based on my own moral standards",
                        5: "I'm neutral and evaluate them based on what they can do or offer",
                        6: "I celebrate our differences and am genuinely curious to learn from them",
                        7: "I understand their views are shaped by their own unique background and stage of development",
                        8: "I see their outer differences, but recognize the same core awareness is in all of us",
                        9: "I feel a sense of unity with them that goes deeper than any surface-level differences"
                    }
                },
                {
                    id: 20,
                    category: "Success & Failure",
                    question: "How do you define success and handle failure?",
                    options: {
                        1: "Success is surviving; failure is a threat to my existence",
                        2: "Success is having strong relationships; failure is letting my people down",
                        3: "Success is winning; failure is a sign of weakness",
                        4: "Success is being morally upright; failure is doing something wrong",
                        5: "Success is achieving my goals; failure is just feedback to improve my strategy",
                        6: "Success is when everyone thrives; failure is when someone's needs are ignored",
                        7: "Success and failure are limited labels; I'm more interested in overall systemic health",
                        8: "Success and failure are just stories I tell myself about what happened",
                        9: "They are just two different experiences on the journey of life, neither good nor bad"
                    }
                },
                {
                    id: 21,
                    category: "Time & Planning",
                    question: "How do you approach time and planning for the future?",
                    options: {
                        1: "I focus on the immediate future, just getting through today and this week",
                        2: "I follow the rhythms and traditions of my family or community",
                        3: "I plan aggressively to gain an advantage and control my future",
                        4: "I schedule my time based on my duties and responsibilities",
                        5: "I create long-term strategic plans with clear goals and timelines",
                        6: "My plans are flexible to accommodate the needs and feelings of others",
                        7: "I think about the future in terms of long-term trends and system development",
                        8: "I hold my plans lightly and stay ready to adapt to what's happening now",
                        9: "I trust that the right actions will unfold in their own time as I stay present"
                    }
                },
                {
                    id: 22,
                    category: "Health & Body",
                    question: "How do you relate to your physical health and body?",
                    options: {
                        1: "I just try to keep it running so I can survive",
                        2: "I take care of it so I can be there for my family and friends",
                        3: "I train it for strength and appearance to give me a competitive edge",
                        4: "I see keeping it healthy as a moral duty and responsibility",
                        5: "I optimize it for peak performance to help me achieve my goals",
                        6: "I honor my body and also care about health access and fairness for all people",
                        7: "I see my body as a complex biological system connected to the larger environment",
                        8: "I'm fascinated by the connection between my mind's awareness and my body's sensations",
                        9: "I experience my body as a temporary vessel for my consciousness"
                    }
                },
                {
                    id: 23,
                    category: "Technology & Innovation",
                    question: "What's your take on new technologies and innovations?",
                    options: {
                        1: "I'm cautious; I'll only use it if it helps me survive or feels safe",
                        2: "I'll adopt it if my friends or community are all using it",
                        3: "I look for ways to use it to get ahead of others or gain power",
                        4: "I judge it based on whether it's morally good or bad for society",
                        5: "I quickly adopt new tools that can help me achieve my goals faster",
                        6: "I'm concerned about its impact on everyone and if access to it is fair",
                        7: "I see it as a key part of how human culture and consciousness evolve",
                        8: "I see it as an external expression of how the human mind works",
                        9: "I use whatever is helpful for living a conscious life, without attachment"
                    }
                },
                {
                    id: 24,
                    category: "Emotions & Feelings",
                    question: "How do you typically relate to your own emotions?",
                    options: {
                        1: "I'm driven by them; my feelings are intense and urgent",
                        2: "I share them with people I trust to maintain our bond",
                        3: "I use them to my advantage, or I suppress them to appear strong",
                        4: "I try to control them and only show what's appropriate",
                        5: "I manage them so they don't interfere with my goals",
                        6: "I accept all of my emotions as valid and try to understand them",
                        7: "I see my emotions as valuable signals about my needs and the state of my environment",
                        8: "I can witness my emotions as they come and go, without being controlled by them",
                        9: "I feel my emotions, but they don't disturb my underlying sense of peace"
                    }
                },
                {
                    id: 25,
                    category: "Global & Environmental Issues",
                    question: "When you think about huge global problems like climate change, what's your perspective?",
                    options: {
                        1: "I'm mostly worried about how it will directly affect my own safety and resources",
                        2: "My opinion is generally the same as my community or the people I trust",
                        3: "I look for how I can personally avoid the costs or even benefit from the changes",
                        4: "I think about it in terms of what our moral duty is according to a higher authority",
                        5: "I look for the most effective, science-based solutions to the problem",
                        6: "I'm focused on climate justice and protecting the most vulnerable people and species",
                        7: "I focus on fixing the deep, interconnected problems in our global systems that cause the issue",
                        8: "I see how our collective mindset has created the problem, and changing that is the key",
                        9: "I try to act in a way that serves the healing of the planet from a place of deep connection"
                    }
                }
            ]
        };

        // Assessment state
        let responses = {};
        let currentQuestion = 0;

        // Start the assessment
        function startAssessment() {
            document.getElementById('intro-section').classList.add('hidden');
            document.getElementById('assessment-section').classList.remove('hidden');
            displayQuestions();
        }

        // Display all questions
        function displayQuestions() {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';

            assessmentData.questions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-container';
                questionDiv.innerHTML = `
                    <div class="question-header">
                        <div class="question-number">Question ${question.id}</div>
                        <div class="question-category">${question.category}</div>
                    </div>
                    <div class="question-text">${question.question}</div>
                    <div class="options-container" data-question="${question.id}">
                        ${Object.entries(question.options).map(([level, text]) => `
                            <div class="option" data-level="${level}" onclick="selectOption(${question.id}, ${level})">
                                <div class="option-number">${level}</div>
                                <div class="option-text">${text}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        // Handle option selection
        function selectOption(questionId, level) {
            responses[questionId] = parseInt(level);

            // Update visual selection
            const container = document.querySelector(`[data-question="${questionId}"]`);
            container.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            container.querySelector(`[data-level="${level}"]`).classList.add('selected');

            updateProgress();

            // Show submit button when all questions are answered
            if (Object.keys(responses).length === assessmentData.questions.length) {
                document.getElementById('submitButton').classList.remove('hidden');
                document.getElementById('submitButton').scrollIntoView({behavior: 'smooth'});
            }
        }

        // Update progress bar
        function updateProgress() {
            const progress = (Object.keys(responses).length / assessmentData.questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent =
                `Question ${Object.keys(responses).length} of ${assessmentData.questions.length}`;
        }

        // Calculate and display results
        function calculateResults() {
            const responseValues = Object.values(responses);

            // Calculate level distribution
            const levelCounts = {};
            for (let i = 1; i <= 9; i++) {
                levelCounts[i] = 0;
            }
            responseValues.forEach(level => {
                levelCounts[level] = (levelCounts[level] || 0) + 1;
            });

            // Find primary level (most frequent)
            let primaryLevel = 1;
            let maxCount = 0;
            for (let level = 1; level <= 9; level++) {
                if (levelCounts[level] > maxCount) {
                    maxCount = levelCounts[level];
                    primaryLevel = level;
                }
            }

            // Calculate average
            const averageLevel = (responseValues.reduce((a, b) => a + b, 0) / responseValues.length).toFixed(1);

            displayResults(primaryLevel, levelCounts, averageLevel);
        }

        // Display results
        function displayResults(primaryLevel, levelCounts, averageLevel) {
            const level = assessmentData.levels[primaryLevel];
            const resultsDiv = document.getElementById('results-section');

            // Build distribution HTML
            let distributionHtml = '';
            for (let i = 1; i <= 9; i++) {
                const count = levelCounts[i] || 0;
                const percentage = ((count / 25) * 100).toFixed(1);
                if (count > 0) {
                    distributionHtml += `
                        <div class="distribution-item">
                            <span>Level ${i}: ${assessmentData.levels[i].name}</span>
                            <div class="distribution-bar">
                                <div class="distribution-fill" style="width: ${percentage}%"></div>
                            </div>
                            <span>${count} responses (${percentage}%)</span>
                        </div>
                    `;
                }
            }

            resultsDiv.innerHTML = `
                <div class="results">
                    <h2>Your Assessment Results</h2>

                    <div class="primary-result">
                        <h3>Your Primary Level: ${primaryLevel}</h3>
                        <p><strong>${level.name}</strong></p>
                        <p>${level.description}</p>
                    </div>

                    <div class="level-distribution">
                        <h4>Response Distribution</h4>
                        ${distributionHtml}
                    </div>

                    <div class="interpretation">
                        <h4>Interpretation</h4>
                        <p><strong>Primary Level ${primaryLevel} (${level.name}):</strong> ${level.interpretation}</p>
                        <p><strong>Average Level:</strong> ${averageLevel}</p>
                        <p><strong>Key Characteristics:</strong> ${level.characteristics.join(', ')}</p>
                    </div>

                    <div class="next-steps">
                        <h4>Next Steps for Development</h4>
                        <p>Consider exploring the characteristics of Level ${Math.min(9, parseInt(primaryLevel) + 1)}
                        to understand your next developmental edge. Remember that growth happens gradually through
                        all levels, not by skipping stages.</p>
                        <p><strong>Important:</strong> Higher levels aren't "better" - each serves important functions
                        in human development. This assessment shows your center of gravity when focusing intensely.</p>
                    </div>

                    <div class="disclaimer">
                        <h4>Important Notes</h4>
                        <p><strong>This is a free alternative to selfmax.ai's Level Check assessment.</strong>
                        Based on publicly available information about hoe_math's framework.</p>
                        <p>For the official assessment with additional features and professional interpretation,
                        visit <a href="https://selfmax.ai" target="_blank">selfmax.ai</a></p>
                        <p>Consider this one data point in understanding yourself. Most people operate from
                        multiple levels depending on context.</p>
                    </div>
                </div>
            `;

            // Hide assessment section and show results
            document.getElementById('assessment-section').classList.add('hidden');
            resultsDiv.classList.remove('hidden');
            resultsDiv.scrollIntoView({behavior: 'smooth'});
        }
    </script>
</body>
</html>
