# Let me create additional questions to make a more comprehensive 25-question assessment
# and provide the complete HTML/JavaScript implementation

additional_questions = [
    {
        "id": 11,
        "category": "Values & Ethics",
        "question": "What guides your moral and ethical decisions?",
        "options": {
            1: "What keeps me alive and safe",
            2: "What my group/tribe considers right",
            3: "What serves my personal interests and power",
            4: "Universal moral principles and divine commands",
            5: "Rational analysis of consequences and effectiveness",
            6: "Caring for all beings and social justice",
            7: "What serves the evolution of all systems involved",
            8: "Awareness of the constructed nature of all moral systems",
            9: "What emerges spontaneously from pure compassion"
        }
    },
    {
        "id": 12,
        "category": "Spirituality & Transcendence",
        "question": "How do you relate to spirituality or transcendence?",
        "options": {
            1: "Pray for protection and basic needs",
            2: "Follow traditional group religious practices",
            3: "Use spiritual practices for personal power/advantage",
            4: "Adhere to orthodox religious doctrine and commandments",
            5: "Explore spiritual practices that enhance performance",
            6: "Embrace diverse spiritual traditions as equally valid",
            7: "See spirituality as part of human developmental evolution",
            8: "Investigate the nature of consciousness and awareness itself",
            9: "Rest as pure awareness - the source of all spiritual seeking"
        }
    },
    {
        "id": 13,
        "category": "Communication Style",
        "question": "How do you typically communicate in important conversations?",
        "options": {
            1: "Direct and blunt to get basic needs met",
            2: "Harmonious and supportive to maintain group bonds",
            3: "Persuasive and dominant to win arguments",
            4: "Proper and formal following social protocols",
            5: "Strategic and clear to achieve objectives",
            6: "Inclusive and empathetic honoring all voices",
            7: "Multi-perspective aware of different developmental levels",
            8: "Transparent about mental constructs and assumptions",
            9: "Speaking from silence, letting wisdom flow through"
        }
    },
    {
        "id": 14,
        "category": "Career & Work",
        "question": "What motivates you most in your work or career?",
        "options": {
            1: "Having job security and meeting basic needs",
            2: "Being part of a team and helping colleagues",
            3: "Gaining status, recognition and beating competition",
            4: "Fulfilling duties and following proper procedures",
            5: "Achieving ambitious goals and professional success",
            6: "Making a positive difference for all stakeholders",
            7: "Contributing to sustainable, integral solutions",
            8: "Expressing creativity while staying unattached to outcomes",
            9: "Serving as an instrument of consciousness in the world"
        }
    },
    {
        "id": 15,
        "category": "Money & Resources",
        "question": "How do you view money and material resources?",
        "options": {
            1: "Essential for survival and basic security",
            2: "Means to support my family and community",
            3: "Tools for gaining power and status over others",
            4: "Rewards for hard work and moral living",
            5: "Resources to achieve my goals and fund my vision",
            6: "Should be shared equitably to reduce suffering",
            7: "Energy in complex economic and social systems",
            8: "Mental constructs we collectively agree have value",
            9: "Temporary forms that consciousness takes - neither good nor bad"
        }
    },
    {
        "id": 16,
        "category": "Personal Growth",
        "question": "How do you approach personal development and growth?",
        "options": {
            1: "Learn skills needed for survival and safety",
            2: "Grow in ways that serve my relationships and tribe",
            3: "Develop capabilities that increase my power and influence",
            4: "Follow established self-improvement programs and principles",
            5: "Strategically develop skills that advance my goals",
            6: "Explore diverse approaches honoring multiple wisdom traditions",
            7: "Understand development as evolution through various levels",
            8: "Investigate the one who seeks to grow and develop",
            9: "Growth happens naturally as awareness recognizes itself"
        }
    },
    {
        "id": 17,
        "category": "Authority & Power",
        "question": "How do you relate to authority figures and power structures?",
        "options": {
            1: "Submit when necessary for protection, resist when safe",
            2: "Respect traditional elders and group hierarchies",
            3: "Challenge them to assert my own dominance",
            4: "Honor legitimate authority and proper chain of command",
            5: "Leverage them strategically for my objectives",
            6: "Question unjust hierarchies and advocate for equality",
            7: "See them as developmental expressions serving different functions",
            8: "Recognize them as mental constructs we collectively maintain",
            9: "Neither submit to nor resist - flow with what's present"
        }
    },
    {
        "id": 18,
        "category": "Stress & Pressure",
        "question": "How do you handle high stress or pressure situations?",
        "options": {
            1: "Fight, flight, or freeze based on survival instincts",
            2: "Seek support and comfort from trusted relationships",
            3: "Dominate the situation or attack the source of pressure",
            4: "Follow established protocols and do my duty despite stress",
            5: "Stay focused on goals and find strategic solutions",
            6: "Consider impact on all stakeholders and seek collaborative solutions",
            7: "See stress as information about systemic imbalances",
            8: "Observe stress arising in awareness without identification",
            9: "Rest in the peace that's always present regardless of circumstances"
        }
    },
    {
        "id": 19,
        "category": "Diversity & Differences",
        "question": "How do you respond to people very different from you?",
        "options": {
            1: "Feel suspicious or threatened by differences",
            2: "Accept those who fit with my group's values",
            3: "See if I can use their differences to my advantage",
            4: "Judge them according to proper moral standards",
            5: "Evaluate how they might help or hinder my goals",
            6: "Celebrate diversity and learn from all perspectives",
            7: "Understand differences as expressions of developmental levels",
            8: "See all differences as constructs of mind - same awareness underneath",
            9: "Rest in the unity that includes and transcends all differences"
        }
    },
    {
        "id": 20,
        "category": "Success & Failure",
        "question": "How do you define success and handle failure?",
        "options": {
            1: "Success is staying alive; failure threatens survival",
            2: "Success is group harmony; failure hurts relationships",
            3: "Success is winning and dominating; failure shows weakness",
            4: "Success is moral righteousness; failure means I sinned",
            5: "Success is achieving goals; failure is feedback for strategy",
            6: "Success is collective wellbeing; failure ignores someone's needs",
            7: "Success is systemic health; failure comes from partial perspectives",
            8: "Success and failure are mental constructs about what is",
            9: "Success and failure are equal movements in consciousness"
        }
    },
    {
        "id": 21,
        "category": "Time & Planning",
        "question": "How do you approach time management and future planning?",
        "options": {
            1: "Focus on immediate needs, short-term survival planning",
            2: "Follow traditional cycles and group schedules",
            3: "Plan to maximize personal advantage and control",
            4: "Organize time according to proper duties and moral obligations",
            5: "Strategic planning with clear timelines to achieve objectives",
            6: "Balance time to honor everyone's needs and schedules",
            7: "Plan considering multiple systems and their development over time",
            8: "Hold plans lightly, staying present to what's emerging now",
            9: "Rest in timeless presence while letting appropriate action unfold"
        }
    },
    {
        "id": 22,
        "category": "Health & Body",
        "question": "How do you relate to your physical health and body?",
        "options": {
            1: "Keep it functional for basic survival needs",
            2: "Care for it as part of caring for family and community",
            3: "Develop it for strength, attractiveness, and competitive advantage",
            4: "Maintain it properly as a moral and social responsibility",
            5: "Optimize it strategically for peak performance and goal achievement",
            6: "Honor it while being mindful of health equity and accessibility",
            7: "See it as part of larger ecological and social systems",
            8: "Observe the relationship between awareness and physical sensations",
            9: "Rest as the awareness in which body sensations appear"
        }
    },
    {
        "id": 23,
        "category": "Technology & Innovation",
        "question": "How do you approach new technologies and innovations?",
        "options": {
            1: "Adopt what helps with basic needs, avoid what threatens security",
            2: "Follow what my trusted community adopts",
            3: "Use technology to gain competitive advantage and power",
            4: "Evaluate based on moral implications and proper use",
            5: "Strategically adopt technologies that advance my objectives",
            6: "Consider impact on all people and advocate for equitable access",
            7: "Understand technology as part of cultural and cognitive evolution",
            8: "See technology as extension of mind's creative capacity",
            9: "Neither attracted to nor averse - use what serves conscious expression"
        }
    },
    {
        "id": 24,
        "category": "Emotions & Feelings",
        "question": "How do you typically relate to your emotions?",
        "options": {
            1: "Feel them intensely, driven by basic survival emotions",
            2: "Share them with trusted others to maintain connection",
            3: "Use them strategically or suppress them to maintain power",
            4: "Control them according to what's socially appropriate",
            5: "Manage them effectively to support goal achievement",
            6: "Honor all emotions as valid and seek to understand others'",
            7: "See emotions as information about system states and needs",
            8: "Witness emotions arising and passing in awareness",
            9: "Rest as the peace that holds all emotional weather"
        }
    },
    {
        "id": 25,
        "category": "Global & Environmental Issues", 
        "question": "How do you approach global challenges like climate change?",
        "options": {
            1: "Focus on how they threaten my immediate safety and resources",
            2: "Support what my community/tribe believes about these issues",
            3: "Look for opportunities to gain advantage or avoid personal cost",
            4: "Follow established authorities and moral guidelines on these issues",
            5: "Analyze rationally and support effective solutions",
            6: "Advocate for justice and protection of all affected beings",
            7: "Address root systemic causes and design integral solutions",
            8: "See these as constructs while responding with compassion",
            9: "Serve the healing of the whole from pure presence"
        }
    }
]

# Combine all questions
all_questions = assessment_questions + additional_questions

print(f"=== COMPLETE 25-QUESTION LEVEL CHECK ASSESSMENT ===")
print(f"Free Alternative to selfmax.ai\n")

print(f"TOTAL QUESTIONS: {len(all_questions)}")
print(f"DOMAINS COVERED: {len(set(q['category'] for q in all_questions))}")

domains = set(q['category'] for q in all_questions)
print(f"\nDOMAINS INCLUDED:")
for domain in sorted(domains):
    count = len([q for q in all_questions if q['category'] == domain])
    print(f"• {domain}: {count} question{'s' if count > 1 else ''}")

# Create the complete web-based implementation
html_implementation = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Level Check Assessment - Alternative to selfmax.ai</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 10px;
        }
        .question-container {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .question-header {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .question-text {
            font-size: 18px;
            margin-bottom: 15px;
            color: #34495e;
        }
        .option {
            margin: 8px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .option:hover {
            background-color: #e8f4fd;
        }
        .option.selected {
            background-color: #3498db;
            color: white;
            border-color: #2980b9;
        }
        .submit-button {
            background-color: #27ae60;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            display: block;
            margin: 30px auto;
        }
        .submit-button:hover {
            background-color: #229954;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        .level-info {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin: 15px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: #3498db;
            border-radius: 10px;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Free Level Check Assessment</h1>
        <p><strong>Alternative to selfmax.ai Level Check</strong></p>
        <p>Based on hoe_math's 9 Levels Framework</p>
        <p>Discover your highest level of thinking when concentrating</p>
    </div>

    <div id="instructions">
        <h2>Instructions</h2>
        <ul>
            <li>This assessment measures your <strong>highest level</strong> of thinking, not your average</li>
            <li>Choose responses that represent you when you're <strong>at your best</strong> and most conscious</li>
            <li>Don't try to score "high" - be honest for accurate results</li>
            <li>Take your time and trust your first strong resonance</li>
            <li>Remember: Higher levels aren't "better" - each serves important functions</li>
        </ul>
        <button onclick="startAssessment()" class="submit-button">Start Assessment</button>
    </div>

    <div id="assessment" style="display: none;">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div id="questionsContainer"></div>
        <button onclick="calculateResults()" class="submit-button" id="submitButton" style="display: none;">Get My Results</button>
    </div>

    <div id="results" class="results"></div>

    <script>
        // Complete assessment data would be inserted here
        const assessment = ''' + json.dumps({
            "questions": all_questions,
            "levels": levels_framework
        }, indent=2) + ''';
        
        let responses = {};
        let currentQuestion = 0;

        function startAssessment() {
            document.getElementById('instructions').style.display = 'none';
            document.getElementById('assessment').style.display = 'block';
            displayQuestions();
        }

        function displayQuestions() {
            const container = document.getElementById('questionsContainer');
            container.innerHTML = '';
            
            assessment.questions.forEach((question, index) => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-container';
                questionDiv.innerHTML = `
                    <div class="question-header">Question ${question.id}: ${question.category}</div>
                    <div class="question-text">${question.question}</div>
                    <div class="options-container" data-question="${question.id}">
                        ${Object.entries(question.options).map(([level, text]) => `
                            <div class="option" data-level="${level}" onclick="selectOption(${question.id}, ${level})">
                                <strong>${level}.</strong> ${text}
                            </div>
                        `).join('')}
                    </div>
                `;
                container.appendChild(questionDiv);
            });
        }

        function selectOption(questionId, level) {
            responses[questionId] = level;
            
            // Update visual selection
            const container = document.querySelector(`[data-question="${questionId}"]`);
            container.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            container.querySelector(`[data-level="${level}"]`).classList.add('selected');
            
            updateProgress();
            
            if (Object.keys(responses).length === assessment.questions.length) {
                document.getElementById('submitButton').style.display = 'block';
                document.getElementById('submitButton').scrollIntoView({behavior: 'smooth'});
            }
        }

        function updateProgress() {
            const progress = (Object.keys(responses).length / assessment.questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function calculateResults() {
            const responseValues = Object.values(responses);
            
            // Calculate level distribution
            const levelCounts = {};
            responseValues.forEach(level => {
                levelCounts[level] = (levelCounts[level] || 0) + 1;
            });
            
            // Find primary level
            const primaryLevel = Object.entries(levelCounts)
                .reduce((a, b) => levelCounts[a[0]] > levelCounts[b[0]] ? a : b)[0];
            
            // Calculate average
            const averageLevel = (responseValues.reduce((a, b) => a + b, 0) / responseValues.length).toFixed(1);
            
            displayResults(primaryLevel, levelCounts, averageLevel);
        }

        function displayResults(primaryLevel, levelCounts, averageLevel) {
            const level = assessment.levels[primaryLevel];
            const resultsDiv = document.getElementById('results');
            
            let distributionHtml = '';
            for (let i = 1; i <= 9; i++) {
                const count = levelCounts[i] || 0;
                const percentage = ((count / 25) * 100).toFixed(1);
                if (count > 0) {
                    distributionHtml += `<p>Level ${i}: ${count} responses (${percentage}%)</p>`;
                }
            }
            
            resultsDiv.innerHTML = `
                <h2>Your Level Check Results</h2>
                
                <div class="level-info">
                    <h3>Primary Level: ${primaryLevel} - ${level.name}</h3>
                    <p><strong>Description:</strong> ${level.description}</p>
                    <p><strong>Key Characteristics:</strong> ${level.characteristics.join(', ')}</p>
                    <p><strong>Typical Responses:</strong> ${level.typical_responses}</p>
                </div>
                
                <h3>Additional Insights</h3>
                <p><strong>Average Level:</strong> ${averageLevel}</p>
                <p><strong>Total Questions Answered:</strong> 25</p>
                
                <h3>Level Distribution</h3>
                ${distributionHtml}
                
                <h3>What This Means</h3>
                <p>Your primary result suggests you most frequently operate from <strong>${level.name}</strong> thinking. This is your 'center of gravity' - where you default when making important decisions.</p>
                
                <p><strong>Remember:</strong></p>
                <ul>
                    <li>Higher levels aren't "better" - each serves important functions</li>
                    <li>You likely access multiple levels depending on the situation</li>
                    <li>This represents your peak capacity, not your average daily operation</li>
                    <li>Growth happens gradually through all levels</li>
                </ul>
                
                <h3>Next Steps</h3>
                <p>Consider exploring the characteristics of Level ${Math.min(9, parseInt(primaryLevel) + 1)} to understand your next developmental edge.</p>
                
                <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 5px;">
                    <p><strong>This is a free alternative to selfmax.ai's Level Check assessment.</strong> 
                    Based on publicly available information about hoe_math's framework. 
                    For the official assessment and additional features, visit selfmax.ai</p>
                </div>
            `;
            
            resultsDiv.style.display = 'block';
            resultsDiv.scrollIntoView({behavior: 'smooth'});
        }
    </script>
</body>
</html>'''

print(f"\nCOMPLETE WEB-BASED IMPLEMENTATION CREATED!")
print(f"Features:")
print(f"• {len(all_questions)} comprehensive questions")
print(f"• Interactive web interface")
print(f"• Real-time progress tracking")
print(f"• Detailed results and interpretation")
print(f"• Mobile-responsive design")
print(f"• No server required - runs entirely in browser")

print(f"\nTo implement:")
print(f"1. Save the HTML code as 'level-check-assessment.html'")
print(f"2. Open in any web browser")
print(f"3. Complete the assessment")
print(f"4. Get instant results and interpretation")

print(f"\nThis provides a complete, free alternative to selfmax.ai's Level Check assessment!")