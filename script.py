# Let me create the assessment tool structure and scoring system without the interactive input
# This will provide the complete framework for users to implement

import json

# Define the 9 levels framework
levels_framework = {
    1: {
        "name": "Desire (Survive)",
        "description": "Basic needs and wants - survival focused",
        "characteristics": ["Immediate needs", "Survival instinct", "Fight/flight responses", "Basic drives"],
        "typical_responses": "Focus on safety, security, and immediate gratification"
    },
    2: {
        "name": "Connect (Appease)", 
        "description": "Understanding others have needs - social connection",
        "characteristics": ["Tribal bonds", "Group loyalty", "Social harmony", "Belonging"],
        "typical_responses": "Prioritize group cohesion and avoiding conflict"
    },
    3: {
        "name": "Take (Power/Egocentric)",
        "description": "Manipulating social connections for personal gain",
        "characteristics": ["Personal power", "Dominance", "Winning", "Control"],
        "typical_responses": "Assert dominance and pursue personal advantage"
    },
    4: {
        "name": "Belong (Conform)",
        "description": "Following rules to fit in - conformity",
        "characteristics": ["Rules and order", "Duty", "Authority", "Conformity"],
        "typical_responses": "Follow established procedures and social expectations"
    },
    5: {
        "name": "Achieve (Individualistic)",
        "description": "Creating your own success path - achievement oriented",
        "characteristics": ["Success", "Achievement", "Strategy", "Individual excellence"],
        "typical_responses": "Set and pursue ambitious personal goals strategically"
    },
    6: {
        "name": "Include (Pluralistic)",
        "description": "Validating all perspectives - inclusivity",
        "characteristics": ["Diversity", "Inclusion", "Multiple perspectives", "Egalitarianism"],
        "typical_responses": "Seek to understand and include all viewpoints"
    },
    7: {
        "name": "Integrate (Systematic)",
        "description": "Understanding how different levels work together",
        "characteristics": ["Systems thinking", "Integration", "Complexity", "Developmental awareness"],
        "typical_responses": "See how different perspectives and systems interconnect"
    },
    8: {
        "name": "Construct-Aware",
        "description": "Witnessing mind construction in real-time",
        "characteristics": ["Metacognition", "Mental construction awareness", "Witnessing", "Deconstruction"],
        "typical_responses": "Observe how mind creates reality and meaning"
    },
    9: {
        "name": "Meta-Systemic",
        "description": "Highest documented level - transcendent perspective",
        "characteristics": ["Transcendence", "Unity consciousness", "Meta-perspective", "Non-dual awareness"],
        "typical_responses": "Operate from pure awareness beyond all constructs"
    }
}

# Complete assessment questions
assessment_questions = [
    {
        "id": 1,
        "category": "Problem Solving",
        "question": "When facing a major life challenge, what's your typical approach?",
        "options": {
            1: "Focus on immediate survival and getting through it",
            2: "Consider how it affects my relationships and seek support",
            3: "Look for ways to gain advantage or control the situation",
            4: "Follow established procedures and social expectations",
            5: "Create a strategic plan to achieve my desired outcome",
            6: "Consider multiple perspectives and seek inclusive solutions",
            7: "Analyze the interconnected systems and root causes",
            8: "Observe my mental patterns and assumptions about the challenge",
            9: "Transcend the problem-solution duality and see the bigger picture"
        }
    },
    {
        "id": 2,
        "category": "Relationships",
        "question": "How do you typically view conflicts with others?",
        "options": {
            1: "Threats to my safety and well-being that must be avoided",
            2: "Disruptions to harmony that need to be smoothed over",
            3: "Opportunities to assert dominance or gain upper hand",
            4: "Violations of proper social rules that need correction",
            5: "Challenges to overcome strategically for better outcomes",
            6: "Chances to understand different viewpoints and find common ground",
            7: "Complex interactions between different developmental levels",
            8: "Projections of internal mental constructs onto external situations",
            9: "Temporary patterns in the flow of consciousness"
        }
    },
    {
        "id": 3,
        "category": "Decision Making",
        "question": "When making important decisions, what matters most to you?",
        "options": {
            1: "What will keep me safe and meet my immediate needs",
            2: "What will maintain my connections and not hurt others",
            3: "What will give me the most power and control",
            4: "What is the right thing according to established principles",
            5: "What will most effectively achieve my goals",
            6: "What honors everyone's perspectives and values",
            7: "What serves the whole system while respecting all parts",
            8: "What emerges from awareness beyond my conditioned patterns",
            9: "What flows naturally from pure consciousness itself"
        }
    },
    {
        "id": 4,
        "category": "Self-Concept",
        "question": "How do you primarily see yourself?",
        "options": {
            1: "A being trying to survive and get basic needs met",
            2: "Someone who belongs to and cares for my tribe/group",
            3: "An individual who must assert myself to get what I want",
            4: "A responsible person who follows proper rules and duties",
            5: "An achiever who creates success through my own efforts",
            6: "A compassionate person who values all beings equally",
            7: "An integral thinker who sees complex interconnections",
            8: "Awareness observing the construction of a 'self'",
            9: "Pure consciousness temporarily appearing as a person"
        }
    },
    {
        "id": 5,
        "category": "Worldview",
        "question": "How do you understand reality?",
        "options": {
            1: "A dangerous place where I must fight for survival",
            2: "A network of relationships and tribal connections", 
            3: "A power game where the strong dominate the weak",
            4: "An ordered system with clear right and wrong ways",
            5: "A field of opportunities to achieve and succeed",
            6: "A rich tapestry of diverse perspectives and experiences",
            7: "A complex web of interconnected systems and levels",
            8: "A construction of mind that can be witnessed and transcended",
            9: "The play of consciousness appearing as multiplicity"
        }
    },
    {
        "id": 6,
        "category": "Leadership Style", 
        "question": "When in a leadership position, how do you tend to operate?",
        "options": {
            1: "Command and control to ensure group survival",
            2: "Build consensus and maintain group harmony",
            3: "Assert authority and demand compliance through force",
            4: "Establish clear rules and ensure everyone follows them",
            5: "Set ambitious goals and motivate high performance",
            6: "Create inclusive environments where all voices are heard",
            7: "Design systems that work for all stakeholders",
            8: "Hold space for emergence while staying unattached to outcomes",
            9: "Serve as an empty vessel for collective wisdom to flow through"
        }
    },
    {
        "id": 7,
        "category": "Learning Approach",
        "question": "How do you prefer to learn and grow?",
        "options": {
            1: "Trial and error focused on immediate practical needs",
            2: "Learning from elders and traditional group wisdom",
            3: "Mastering skills that give me competitive advantage",
            4: "Following established curricula and proven methods",
            5: "Strategic learning that advances my goals efficiently",
            6: "Exploring diverse perspectives and experiential learning",
            7: "Studying complex systems and their interactions",
            8: "Investigating the nature of mind and consciousness itself",
            9: "Learning happens spontaneously through pure awareness"
        }
    },
    {
        "id": 8,
        "category": "Meaning & Purpose",
        "question": "What gives your life the deepest meaning?",
        "options": {
            1: "Staying alive and meeting my basic needs",
            2: "Being connected to and caring for my people",
            3: "Having power and influence over my environment",
            4: "Living according to higher moral principles",
            5: "Achieving my personal goals and vision",
            6: "Contributing to justice and wellbeing for all",
            7: "Participating in the evolution of consciousness",
            8: "Recognizing the constructed nature of all meaning",
            9: "Being an expression of pure consciousness/love"
        }
    },
    {
        "id": 9,
        "category": "Response to Criticism",
        "question": "When someone criticizes you, what's your typical reaction?",
        "options": {
            1: "Feel threatened and defensive about my safety/position",
            2: "Worry about damage to relationships and seek to repair",
            3: "Get angry and look for ways to retaliate or dominate",
            4: "Check if I violated proper rules and correct accordingly",
            5: "Analyze if it's valid feedback for achieving my goals",
            6: "Try to understand their perspective and find mutual ground",
            7: "See it as information about different developmental stages",
            8: "Observe my emotional reactions and mental stories arising",
            9: "Rest in awareness, seeing criticism as just phenomena arising"
        }
    },
    {
        "id": 10,
        "category": "Change & Uncertainty",
        "question": "How do you respond to major changes or uncertainty?",
        "options": {
            1: "Feel anxious and focus on securing basic safety",
            2: "Seek comfort and support from my trusted group",
            3: "Try to control outcomes and dominate the situation",
            4: "Look for clear rules and established ways to handle it",
            5: "See opportunities and strategically adapt my plans",
            6: "Embrace the diversity of possibilities and perspectives",
            7: "Understand it as natural evolution of complex systems",
            8: "Observe attachment to certainty and rest in not-knowing",
            9: "Flow with change as the natural movement of consciousness"
        }
    }
]

def calculate_level(responses):
    """Calculate the dominant level based on responses"""
    if not responses:
        return None
    
    # Count frequency of each level
    level_counts = {}
    for response in responses:
        level_counts[response] = level_counts.get(response, 0) + 1
    
    # Find the most frequent level
    primary_level = max(level_counts, key=level_counts.get)
    
    # Calculate average for additional insight
    average_level = sum(responses) / len(responses)
    
    return {
        "primary_level": primary_level,
        "level_distribution": level_counts,
        "average_level": round(average_level, 1),
        "total_questions": len(responses)
    }

def generate_interpretation(result):
    """Generate interpretation of results"""
    if not result:
        return "Unable to generate interpretation"
    
    primary = result["primary_level"]
    level_info = levels_framework[primary]
    
    interpretation = f"""
=== YOUR LEVEL CHECK RESULTS ===

PRIMARY LEVEL: {primary} - {level_info['name']}
Description: {level_info['description']}

Key Characteristics:
{chr(10).join([f"• {char}" for char in level_info['characteristics']])}

Typical Responses: {level_info['typical_responses']}

ADDITIONAL INSIGHTS:
• Average Level: {result['average_level']}
• You answered from Level {primary} most frequently ({result['level_distribution'][primary]} times)
• This represents your highest/peak thinking level when concentrating

LEVEL DISTRIBUTION:
"""
    
    for level in sorted(result['level_distribution'].keys()):
        count = result['level_distribution'][level]
        percentage = (count / result['total_questions']) * 100
        interpretation += f"Level {level}: {count} responses ({percentage:.1f}%)\n"
    
    interpretation += f"""
WHAT THIS MEANS:
Your primary result suggests you most frequently operate from {level_info['name']} thinking.
This is your 'center of gravity' - where you default when making important decisions.

Remember: 
- Higher levels aren't "better" - each serves important functions
- You likely access multiple levels depending on the situation
- This represents your peak capacity, not your average daily operation
- Growth happens gradually through all levels

NEXT STEPS FOR DEVELOPMENT:
Consider exploring the characteristics of Level {primary + 1 if primary < 9 else primary} 
to understand your next developmental edge.
"""
    
    return interpretation

# Create example results for demonstration
example_responses = [5, 6, 5, 7, 6, 5, 6, 7, 6, 5]  # Example: mostly Level 5-6 responses
example_result = calculate_level(example_responses)
example_interpretation = generate_interpretation(example_result)

print("=== FREE LEVEL CHECK ASSESSMENT TOOL ===")
print("Complete Implementation Ready for Use\n")

print("FRAMEWORK OVERVIEW:")
for level, info in levels_framework.items():
    print(f"Level {level}: {info['name']} - {info['description']}")

print(f"\nASSESSMENT STRUCTURE:")
print(f"• {len(assessment_questions)} comprehensive questions")
print(f"• Covers {len(set(q['category'] for q in assessment_questions))} life domains")
print(f"• Each question maps to all 9 developmental levels")

print(f"\nEXAMPLE RESULT (based on sample responses):")
print(example_interpretation)

print("\n=== IMPLEMENTATION GUIDE ===")
print("""
TO USE THIS TOOL:

1. TAKE THE ASSESSMENT:
   - Review each question carefully
   - Choose the response that most represents your HIGHEST thinking
   - Don't overthink - go with your first strong resonance
   - Be honest rather than trying to score "high"

2. SCORE YOUR RESULTS:
   - Count how many times you selected each level (1-9)
   - Your most frequent level is your primary result
   - Calculate your average level for additional insight

3. INTERPRET YOUR RESULTS:
   - Use the interpretation guide to understand your level
   - Remember this shows your PEAK capacity, not average
   - Consider this one data point in understanding yourself

4. DEVELOP FURTHER:
   - Study the characteristics of your next level up
   - Practice perspective-taking from different levels
   - Join communities focused on integral development
""")

# Save the complete assessment tool
assessment_tool = {
    "framework": levels_framework,
    "questions": assessment_questions,
    "instructions": """
This is a free alternative to selfmax.ai's Level Check assessment, based on hoe_math's 9 Levels framework.

INSTRUCTIONS:
1. For each question, select the response that most resonates with your highest thinking
2. Choose what represents you when you're at your best and most conscious
3. Don't try to score high - be honest for accurate results
4. This measures your peak capacity, not your average daily operation

SCORING:
- Count frequency of each level (1-9) in your responses
- Your most frequent level = your primary level
- Calculate average for additional insight

Remember: Higher levels aren't "better" - each serves important functions in human development.
    """,
    "scoring_method": "frequency_based_with_average"
}

print(f"\nCOMPLETE TOOL READY FOR IMPLEMENTATION!")
print(f"Assessment contains {len(assessment_questions)} questions across {len(set(q['category'] for q in assessment_questions))} domains")
print(f"Free alternative to selfmax.ai Level Check assessment")